#!/usr/bin/env python3
"""
Cursor账号注册脚本 - 使用screenX/screenY补丁绕过Cloudflare验证
"""

import asyncio
import os
from playwright.async_api import async_playwright

async def register_cursor_account():
    """使用Turnstile补丁注册Cursor账号"""
    
    # 扩展路径
    extension_path = os.path.abspath("../cursor_pro/turnstilePatch")
    print(f"扩展路径: {extension_path}")
    
    async with async_playwright() as p:
        # 启动浏览器并加载Turnstile补丁扩展
        browser = await p.chromium.launch(
            headless=False,  # 必须使用有头模式来加载扩展
            args=[
                f'--load-extension={extension_path}',
                f'--disable-extensions-except={extension_path}',
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage',
                '--no-sandbox',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        )
        
        # 创建新页面
        page = await browser.new_page()
        
        # 设置用户代理
        await page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
                         '(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        try:
            print("🚀 开始访问Cursor主页...")
            await page.goto('https://cursor.sh', wait_until='networkidle')
            await asyncio.sleep(2)
            
            print("📸 截图：Cursor主页")
            await page.screenshot(path='cursor_homepage.png')
            
            print("🔍 寻找Sign in链接...")
            sign_in_link = page.locator('a[href="/api/auth/login"]').first
            await sign_in_link.click()
            await asyncio.sleep(3)
            
            print("📸 截图：登录页面")
            await page.screenshot(path='cursor_login_page.png')
            
            print("📧 输入邮箱地址...")
            email_input = page.locator('input[type="email"], input[name="email"]')
            await email_input.fill('<EMAIL>')
            await asyncio.sleep(1)
            
            print("▶️ 点击Continue按钮...")
            continue_button = page.locator('button:has-text("Continue")')
            await continue_button.click()
            await asyncio.sleep(10)  # 等待更长时间观察页面变化
            
            print("📸 截图：邮箱提交后")
            await page.screenshot(path='cursor_after_email.png')

            # 检查当前页面URL和标题
            current_url = page.url
            page_title = await page.title()
            print(f"🌐 当前页面URL: {current_url}")
            print(f"📄 页面标题: {page_title}")

            # 获取页面可见文本来判断状态
            page_text = await page.inner_text('body')
            print(f"📝 页面内容预览: {page_text[:200]}...")

            # 检查是否需要处理Cloudflare验证
            if "Before continuing, we need to be sure you are human" in page_text:
                print("🤖 检测到Cloudflare人机验证，尝试处理...")

                # 等待验证框加载
                await asyncio.sleep(3)

                # 尝试找到并点击Cloudflare验证框
                try:
                    # 方法1：寻找Turnstile验证框
                    turnstile_frame = page.frame_locator('iframe[src*="turnstile"]')
                    if await turnstile_frame.locator('input[type="checkbox"]').is_visible():
                        print("🎯 找到Turnstile验证框，点击验证...")
                        await turnstile_frame.locator('input[type="checkbox"]').click()
                        await asyncio.sleep(5)

                    # 方法2：寻找其他可能的验证元素
                    challenge_elements = [
                        'input[type="checkbox"]',
                        'button[type="submit"]',
                        '.cf-turnstile',
                        '[data-sitekey]'
                    ]

                    for selector in challenge_elements:
                        elements = page.locator(selector)
                        if await elements.count() > 0:
                            print(f"🎯 找到验证元素: {selector}")
                            await elements.first.click()
                            await asyncio.sleep(3)
                            break

                    # 等待验证完成
                    print("⏳ 等待验证完成...")
                    await asyncio.sleep(10)

                except Exception as e:
                    print(f"⚠️ 验证处理出错: {e}")

            # 重新检查页面状态
            current_url = page.url
            page_text = await page.inner_text('body')
            print(f"🔄 验证后URL: {current_url}")

            # 检查是否出现密码字段或验证码选项
            password_field = page.locator('input[type="password"]')
            email_code_button = page.locator('button:has-text("Email sign-in code")')

            if await password_field.is_visible():
                print("🔑 检测到密码字段，选择邮箱验证码登录...")
                if await email_code_button.is_visible():
                    await email_code_button.click()
                    await asyncio.sleep(3)
                    print("✅ 已选择邮箱验证码登录方式")
            
            print("📸 截图：最终状态")
            await page.screenshot(path='cursor_final_state.png')
            
            # 等待用户输入验证码
            print("\n🎉 注册流程已启动！")
            print("📧 请检查邮箱 <EMAIL> 是否收到验证码")
            print("⏳ 浏览器将保持打开状态，等待您输入验证码...")
            
            # 保持浏览器打开，等待用户操作
            await asyncio.sleep(300)  # 等待5分钟
            
        except Exception as e:
            print(f"❌ 注册过程中出现错误: {e}")
            await page.screenshot(path='cursor_error.png')
            
        finally:
            print("🔚 关闭浏览器")
            await browser.close()

if __name__ == "__main__":
    asyncio.run(register_cursor_account())
