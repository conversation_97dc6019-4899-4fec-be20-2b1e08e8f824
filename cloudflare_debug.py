#!/usr/bin/env python3
"""
Cloudflare验证调试脚本
"""

import asyncio
import os
from playwright.async_api import async_playwright

async def debug_cloudflare():
    """调试Cloudflare验证页面结构"""
    
    extension_path = os.path.abspath("../cursor_pro/turnstilePatch")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(
            headless=False,
            args=[
                f'--load-extension={extension_path}',
                f'--disable-extensions-except={extension_path}',
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage',
                '--no-sandbox'
            ]
        )
        
        page = await browser.new_page()
        
        try:
            # 直接访问验证页面
            print("🔍 访问Cursor登录页面...")
            await page.goto('https://cursor.sh/api/auth/login')
            await asyncio.sleep(5)
            
            # 输入邮箱
            print("📧 输入邮箱...")
            await page.fill('input[type="email"]', '<EMAIL>')
            await page.click('button:has-text("Continue")')
            await asyncio.sleep(5)
            
            # 分析页面结构
            print("🔍 分析页面结构...")
            
            # 获取所有iframe
            frames = page.frames
            print(f"📄 页面包含 {len(frames)} 个frame")
            
            for i, frame in enumerate(frames):
                try:
                    frame_url = frame.url
                    print(f"Frame {i}: {frame_url}")
                except:
                    print(f"Frame {i}: 无法获取URL")
            
            # 查找Cloudflare相关元素
            print("🔍 查找Cloudflare元素...")
            
            # 检查页面HTML
            html_content = await page.content()
            if 'turnstile' in html_content.lower():
                print("✅ 发现Turnstile相关内容")
            if 'cloudflare' in html_content.lower():
                print("✅ 发现Cloudflare相关内容")
            
            # 查找可能的验证元素
            selectors_to_check = [
                'iframe[src*="turnstile"]',
                'iframe[src*="cloudflare"]',
                '.cf-turnstile',
                '[data-sitekey]',
                'input[type="checkbox"]',
                'div[id*="turnstile"]',
                'div[class*="turnstile"]'
            ]
            
            for selector in selectors_to_check:
                elements = page.locator(selector)
                count = await elements.count()
                if count > 0:
                    print(f"✅ 找到元素: {selector} (数量: {count})")
                    
                    # 尝试获取元素属性
                    try:
                        first_element = elements.first
                        if await first_element.is_visible():
                            print(f"   - 元素可见")
                        else:
                            print(f"   - 元素不可见")
                    except Exception as e:
                        print(f"   - 检查可见性失败: {e}")
            
            # 截图保存当前状态
            await page.screenshot(path='cloudflare_debug.png', full_page=True)
            print("📸 已保存调试截图: cloudflare_debug.png")
            
            # 等待用户观察
            print("⏳ 等待30秒供观察...")
            await asyncio.sleep(30)
            
        except Exception as e:
            print(f"❌ 调试过程出错: {e}")
            await page.screenshot(path='cloudflare_debug_error.png')
            
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_cloudflare())
